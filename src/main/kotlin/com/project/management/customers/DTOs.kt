package com.project.management.customers

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.project.management.projects.requests.PatchRequestProjectIncomeAmount

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class CustomerPostRequest(
    val name: String,
    val phoneNumber: String,
    val secondaryPhoneNumber: String?,
)

data class CustomerPatchRequest(
    val name: String?,
    val phoneNumber: String?,
    val version: Long
)

data class CustomerTransactionRequestDto(
    val amount: Double,
    val amountPaid: Double,
    val description: String,
    val projectId: Long,
    val transactionDate: String
)

data class PatchCustomerTransactionRequestDto(
    val description: String? = null,
    val version: Long
)

class PostRequestProjectIncomePay (
    val amountPaid: Double,
    val version: Long,
) {
    fun toModifyAmount(amount: Double): PatchRequestProjectIncomeAmount {
        return PatchRequestProjectIncomeAmount(
            amount = amount,
            amountPaid = amountPaid,
            transactionVersion = version
        )
    }
}