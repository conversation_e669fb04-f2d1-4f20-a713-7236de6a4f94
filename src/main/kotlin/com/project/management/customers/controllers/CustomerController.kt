package com.project.management.customers.controllers

import com.project.management.customers.CustomerPatchRequest
import com.project.management.customers.CustomerPostRequest
import com.project.management.customers.Customer
import com.project.management.customers.services.CustomerService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/customers")
class CustomerController(
    private val customerService: CustomerService
) {
    @GetMapping
    fun getAll(): ResponseEntity<List<Customer>> {
        return ResponseEntity.ok(customerService.getAll())
    }

    @GetMapping("/{customerId}")
    fun getCustomer(@PathVariable customerId: Long): ResponseEntity<Customer> {
        return ResponseEntity.ok(customerService.getCustomerById(customerId))
    }

    @PostMapping
    fun create(@RequestBody customerRequestDto: CustomerPostRequest): ResponseEntity<Customer> {
        return ResponseEntity.ok(customerService.create(customerRequestDto))
    }

    @PatchMapping("/{customerId}")
    fun edit(
        @RequestBody request: CustomerPatchRequest,
        @PathVariable customerId: Long
    ): ResponseEntity<Customer> {
        return ResponseEntity.ok(customerService.edit(request, customerId))
    }
}