package com.project.management.customers.controllers

import com.project.management.customers.CustomerTransactionRequestDto
import com.project.management.customers.PostRequestProjectIncomePay
import com.project.management.customers.CustomerTransaction
import com.project.management.customers.PatchCustomerTransactionRequestDto
import com.project.management.customers.services.CustomerTransactionService
import com.project.management.money.MoneyService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/customers/{customerId}/transactions")
class CustomerTransactionController(
    private val customerTransactionService: CustomerTransactionService,
    private val money: MoneyService
) {
    @GetMapping
    fun getAll(
        @PathVariable customerId: Long
    ): ResponseEntity<List<CustomerTransaction>> {
        return ResponseEntity.ok(customerTransactionService.getAllByCustomerId(customerId))
    }

    @PostMapping
    fun create(
        @PathVariable customerId: Long,
        @RequestBody customerTransactionRequestDto: CustomerTransactionRequestDto,
    ): ResponseEntity<CustomerTransaction> {
        return ResponseEntity.ok(
            customerTransactionService.create(
                customerTransactionRequestDto,
                customerId
            )
        )
    }

    @PostMapping("/{customerTransactionId}/pay")
    fun changeAmountPaid(
        @PathVariable customerTransactionId: Long,
        @RequestBody request: PostRequestProjectIncomePay
    ): ResponseEntity<CustomerTransaction> {
        return ResponseEntity.ok(
            money.projectIncomePay(request, customerTransactionId).customerTransaction
        )
    }

    @PatchMapping("/{customerTransactionId}")
    fun modifyTransaction(
        @PathVariable customerTransactionId: Long,
        @RequestBody request: PatchCustomerTransactionRequestDto
    ): ResponseEntity<CustomerTransaction> {
        return ResponseEntity.ok(
            customerTransactionService.updateTransaction(request, customerTransactionId)
        )
    }
}