package com.project.management.customers.mappers

import com.project.management.common.annotation.MappingOrganizationEntity
import com.project.management.customers.requests.CustomerRequestDto
import com.project.management.customers.Customer
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy
import java.math.BigDecimal
import java.time.ZonedDateTime

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface CustomerMapper {

    @MappingOrganizationEntity
    fun toCustomer(
        customerRequestDto: CustomerRequestDto,
        version: Long,
        createdAt: ZonedDateTime = ZonedDateTime.now(),
        updatedAt: ZonedDateTime = createdAt,
        organizationId: Long,
        userId: Long?,
        balanceAccumulator: BigDecimal = BigDecimal.ZERO,
        paidAccumulator: BigDecimal = BigDecimal.ZERO
    ): Customer
}