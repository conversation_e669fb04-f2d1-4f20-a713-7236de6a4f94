package com.project.management.customers.mappers

import com.project.management.common.annotation.MappingOrganizationEntity
import com.project.management.customers.requests.CustomerTransactionRequestDto
import com.project.management.customers.CustomerTransaction
import com.project.management.users.User
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.ReportingPolicy
import java.time.ZonedDateTime

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface CustomerTransactionMapper {

    @MappingOrganizationEntity
    @Mapping(target = "createdByDetails", source = "createdByDetails")
    fun toTransaction(
        customerTransactionRequestDto: CustomerTransactionRequestDto,
        createdAt: ZonedDateTime = ZonedDateTime.now(),
        updatedAt: ZonedDateTime = createdAt,
        createdByDetails: User,
        organizationId: Long,
        customerId: Long,
        version: Long,
        userId: Long?
    ): CustomerTransaction
}