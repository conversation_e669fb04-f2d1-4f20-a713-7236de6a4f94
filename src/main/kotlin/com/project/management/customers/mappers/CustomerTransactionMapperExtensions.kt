package com.project.management.customers.mappers

import com.project.management.customers.requests.CustomerTransactionRequestDto
import com.project.management.customers.CustomerTransaction
import com.project.management.projects.requests.PostRequestProjectIncome
import com.project.management.users.User
import java.time.ZonedDateTime

fun PostRequestProjectIncome.toCustomerTransaction(projectId: Long): CustomerTransactionRequestDto {
    return CustomerTransactionRequestDto(
        amount = amount,
        amountPaid = amountPaid,
        description = description,
        projectId = projectId,
        transactionDate = transactionDate
    )
}

fun CustomerTransactionRequestDto.toEntity(
    customerId: Long,
    user: User,
    version: Long
): CustomerTransaction {
    return CustomerTransaction(
        organizationId = user.organizationId,
        amount = amount.toBigDecimal(),
        amountPaid = amountPaid.toBigDecimal(),
        description = description,
        customerId = customerId,
        projectId = projectId,
        transactionDate = ZonedDateTime.parse(transactionDate),
        createdBy = user.id!!,
        updatedBy = user.id!!
    )
}
