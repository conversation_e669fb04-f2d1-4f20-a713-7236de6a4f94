package com.project.management.customers.repositories

import com.project.management.customers.Customer
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface CustomerRepository : JpaRepository<Customer, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<Customer>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): Customer?

    // SUM queries for customer accumulators
    @Query(
        value = "SELECT COALESCE(SUM(balance_accumulator), 0) FROM customers WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumBalanceAccumulatorByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(paid_accumulator), 0) FROM customers WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumPaidAccumulatorByOrganizationId(organizationId: Long): BigDecimal
}