package com.project.management.customers.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.customers.CustomerPatchRequest
import com.project.management.customers.CustomerPostRequest
import com.project.management.customers.Customer
import com.project.management.customers.toModel
import com.project.management.customers.CustomerRepository
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class CustomerService(
    private val currentUser: CurrentUserConfig,
) {

    fun getAll(): List<Customer> {
        val user = currentUser.getCurrentUser()
        val customers = CustomerRepository.Query.getAll(OrganizationId(user.organizationId))
        return customers
    }

    fun getCustomerById(customerId: Long): Customer {
        val user = currentUser.getCurrentUser()
        val customer = CustomerRepository.Query
            .getById(OrganizationId(user.organizationId), customerId).validate()
        return customer
    }

    @Transactional
    fun create(customerRequestDto: CustomerPostRequest): Customer {
        val user = currentUser.getCurrentUser()
        val customer = customerRequestDto.toModel(user)

        return CustomerRepository.Mutate.save(customer)
    }

    @Transactional
    fun edit(
        request: CustomerPatchRequest,
        customerId: Long
    ): Customer {
        val loggedInUser = currentUser.getCurrentUser()
        val customer = CustomerRepository.Query
            .getById(OrganizationId(loggedInUser.organizationId), customerId).validate()

        if (customer.version != request.version) { throw BusinessException.ConflictException() }

        if (request.name != null) customer.name = request.name
        if (request.phoneNumber != null) {
            customer.phoneNumber = request.phoneNumber
            customer.secondaryPhoneNumber = request.phoneNumber
        }
        customer.updatedBy = loggedInUser.id

        return CustomerRepository.Mutate.save(customer)
    }
}