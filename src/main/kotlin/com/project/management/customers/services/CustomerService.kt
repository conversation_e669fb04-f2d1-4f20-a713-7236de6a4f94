package com.project.management.customers.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.customers.requests.PatchCustomerRequestDto
import com.project.management.customers.requests.CustomerRequestDto
import com.project.management.customers.Customer
import com.project.management.customers.mappers.CustomerMapper
import com.project.management.customers.repositories.CustomerRepository
import com.project.management.customers.validators.CustomerValidator
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class CustomerService(
    private val customerRepository: CustomerRepository,
    private val currentUser: CurrentUserConfig,
    private val customerMapper: CustomerMapper,
    private val customerValidator: CustomerValidator
) {

    fun getAll(): List<Customer> {
        val user = currentUser.getCurrentUser()
        val customers = customerRepository.findAllByOrganizationId(user.organizationId)
        return customers
    }

    fun getCustomerById(customerId: Long): Customer {
        val user = currentUser.getCurrentUser()
        val customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId = customerId,
            organizationId = user.organizationId
        )
        return customer
    }

    @Transactional
    fun create(customerRequestDto: CustomerRequestDto): Customer {
        val user = currentUser.getCurrentUser()
        val customer = customerMapper.toCustomer(
            customerRequestDto,
            organizationId = user.organizationId,
            userId = user.id,
            version = 1,
        )

        return customerRepository.save(customer)
    }

    @Transactional
    fun edit(
        request: PatchCustomerRequestDto,
        customerId: Long
    ): Customer {
        val loggedInUser = currentUser.getCurrentUser()
        val customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId = customerId,
            organizationId = loggedInUser.organizationId
        )
        if (customer.version != request.version) { throw BusinessException.ConflictException() }

        if (request.name != null) customer.name = request.name
        if (request.phoneNumber != null) {
            customer.phoneNumber = request.phoneNumber
            customer.secondaryPhoneNumber = request.phoneNumber
        }
        customer.updatedBy = loggedInUser.id

        return customerRepository.save(customer)
    }
}