package com.project.management.customers.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.customers.CustomerTransactionRequestDto
import com.project.management.customers.CustomerTransaction
import com.project.management.customers.PatchCustomerTransactionRequestDto
import com.project.management.customers.toEntity
import com.project.management.customers.CustomerRepository
import com.project.management.customers.CustomerTransactionRepository
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.projects.services.ProjectIncomesService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service

@Service
class CustomerTransactionService(
    private val currentUser: CurrentUserConfig,
) {

    @Autowired
    @Lazy
    private lateinit var projectIncomes: ProjectIncomesService

    fun getAll(): List<CustomerTransaction> {
        val user = currentUser.getCurrentUser()
        val customerTransactions = CustomerTransactionRepository.Query.getAll(OrganizationId(user.organizationId))
        return customerTransactions
    }

    fun getAllByCustomerId(customerId: Long): List<CustomerTransaction> {
        val user = currentUser.getCurrentUser()
        val customer = CustomerRepository.Query
            .getById(OrganizationId(user.organizationId), customerId).validate()
        val customerTransactions = CustomerTransactionRepository.Query
            .getAllByCustomerId(OrganizationId(user.organizationId), customer.id!!)
        return customerTransactions
    }

    @Transactional
    fun create(transaction: CustomerTransactionRequestDto, customerId: Long): CustomerTransaction {
        val user = currentUser.getCurrentUser()
        val customer = CustomerRepository.Query
            .getById(OrganizationId(user.organizationId), customerId).validate()
        val customerTransaction = transaction.toEntity(
            customerId = customer.id!!, user = user
        )
        val result = CustomerTransactionRepository.Mutate.save(customerTransaction)
        return result
    }

    @Transactional
    fun updateTransaction(
        request: PatchCustomerTransactionRequestDto,
        customerTransactionId: Long
    ): CustomerTransaction {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = CustomerTransactionRepository.Query
            .getById(OrganizationId(loggedInUser.organizationId), customerTransactionId).validate()

        if (transaction.version != request.version) throw BusinessException.ConflictException()

        val updatedTransaction = transaction
        updatedTransaction.description = request.description ?: transaction.description
        updatedTransaction.updatedBy = loggedInUser.id!!

        return CustomerTransactionRepository.Mutate.save(updatedTransaction)
    }

    @Transactional
    fun delete(customerTransactionId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = CustomerTransactionRepository.Query
            .getById(OrganizationId(loggedInUser.organizationId), customerTransactionId).validate()
        CustomerTransactionRepository.Mutate.deleteById(
            id = customerTransactionId,
            organizationId = OrganizationId(loggedInUser.organizationId),
            updatedBy = loggedInUser.id!!
        )
    }
}

