package com.project.management.terms

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.common.entity.OrganizationId
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class TermsService(
    private val currentUser: CurrentUserConfig
) {

    fun getAll(): List<Term> {
        val user = currentUser.getCurrentUser()
        return TermRepository.Query.getAll(OrganizationId(user.organizationId))
    }

    fun getAll(termsGroupId: Long): List<Term> {
        val user = currentUser.getCurrentUser()
        return TermRepository.Query.getAllByTermsGroup(
            OrganizationId(user.organizationId),
            termsGroupId
        )
    }

    @Transactional
    fun create(termRequestDto: TermPostRequest, termsGroupId: Long): Term {
        val user = currentUser.getCurrentUser()
        val termsGroup = TermsGroupRepository.Validate.validateExistsByIdAndOrganizationId(
            termsGroupId,
            user.organizationId
        )
        var term = termRequestDto.toModel(user, termsGroup)
        term = TermRepository.Mutate.save(term)
        return term
    }

    @Transactional
    fun edit(request: TermPatchRequest, termId: Long): Term {
        val loggedInUser = currentUser.getCurrentUser()
        val term = TermRepository.Validate.validateExistsByIdAndOrganizationId(
            termId = termId,
            organizationId = loggedInUser.organizationId
        )
        if (term.version != request.version) throw BusinessException.ConflictException()

        term.name = request.name ?: term.name
        term.description = request.description ?: term.description
        term.updatedBy = loggedInUser.id!!
        return TermRepository.Mutate.save(term)
    }
}

@Service
class TermsGroupsService(
    private val currentUser: CurrentUserConfig
) {
    fun getAll(): List<TermsGroup> {
        val user = currentUser.getCurrentUser()
        return TermsGroupRepository.Query.getAll(OrganizationId(user.organizationId))
    }

    @Transactional
    fun create(termsGroupRequestDto: TermsGroupPostRequest): TermsGroup {
        val user = currentUser.getCurrentUser()
        var termsGroup = termsGroupRequestDto.toModel(user)
        termsGroup = TermsGroupRepository.Mutate.save(termsGroup)
        return termsGroup
    }

    @Transactional
    fun update(request: TermsGroupPatchRequest, termsGroupId: Long): TermsGroup {
        val loggedInUser = currentUser.getCurrentUser()
        val termsGroup = TermsGroupRepository.Validate.validateExistsByIdAndOrganizationId(
            termsGroupId = termsGroupId,
            organizationId = loggedInUser.organizationId
        )
        if (termsGroup.version != request.version) {
            throw BusinessException.ConflictException()
        }

        termsGroup.name = request.name ?: termsGroup.name
        termsGroup.description = request.description ?: termsGroup.description
        termsGroup.updatedBy = loggedInUser.id!!
        return TermsGroupRepository.Mutate.save(termsGroup)
    }
}